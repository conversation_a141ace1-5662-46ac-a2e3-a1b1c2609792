# Citing pertpy

If you find pertpy useful for your research, please consider citing our work as follows:

```bibtex
@article {Heumos2024.08.04.606516,
    author = {<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, Mojtaba and Gold, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, Altana and <PERSON><PERSON>far, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
    title = {Pertpy: an end-to-end framework for perturbation analysis},
    elocation-id = {2024.08.04.606516},
    year = {2024},
    doi = {10.1101/2024.08.04.606516},
    publisher = {Cold Spring Harbor Laboratory},
    URL = {https://www.biorxiv.org/content/early/2024/08/07/2024.08.04.606516},
    eprint = {https://www.biorxiv.org/content/early/2024/08/07/2024.08.04.606516.full.pdf},
    journal = {bioRxiv}
}
```

If you are using any previously published tool, please also cite the original publication.
All tool specific references can be found here: {doc}`../references`.
