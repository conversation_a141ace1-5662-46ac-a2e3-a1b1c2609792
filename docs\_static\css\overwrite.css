/* for the sphinx design cards */
body {
    --sd-color-shadow: dimgrey;
}

dt:target,
span.highlighted {
    background-color: #f0f0f0;
}

dl.citation > dt {
    float: left;
    margin-right: 15px;
    font-weight: bold;
}

/* Parameters normalize size and captialized, */
dl .field-list dt {
    font-size: var(--font-size--normal) !important;
    text-transform: none !important;
}

/* examples and headings in classes */
p.rubric {
    font-size: var(--font-size--normal);
    text-transform: none;
    font-weight: 500;
}

/* adapted from https://github.com/dask/dask-sphinx-theme/blob/main/dask_sphinx_theme/static/css/nbsphinx.css */

.nbinput .prompt,
.nboutput .prompt {
    display: none;
}
.nboutput .stderr {
    display: none;
}

div.nblast.container {
    padding-bottom: 10px !important;
    padding-right: 0px;
    padding-left: 0px;
}

div.nbinput.container {
    padding-top: 10px !important;
    padding-right: 0px;
    padding-left: 0px;
}

div.nbinput.container div.input_area div[class*="highlight"] > pre {
    padding: 10px !important;
    margin: 0;
}

p.topic-title {
    margin-top: 0;
}

/* so that api methods are small in sidebar */
li.toctree-l3 {
    font-size: 81.25% !important;
}
li.toctree-l4 {
    font-size: 75% !important;
}

.bd-sidebar .caption-text {
    color: #e63946;
    font-weight: 600;
    text-transform: uppercase;
}
