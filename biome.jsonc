{
    "$schema": "https://biomejs.dev/schemas/2.0.5/schema.json",
    "formatter": { "useEditorconfig": true },
    "overrides": [
        {
            "includes": [".vscode/**/*.json", "**/*.jsonc", "**/asv.conf.json"],
            "json": {
                "formatter": {
                    "trailingCommas": "all",
                },
                "parser": {
                    "allowComments": true,
                    "allowTrailingCommas": true,
                },
            },
        },
    ],
    "linter": {
        "rules": {
            "style": {
                "noParameterAssign": "error",
                "useAsConstAssertion": "error",
                "useDefaultParameterLast": "error",
                "useEnumInitializers": "error",
                "useSelfClosingElements": "error",
                "useSingleVarDeclarator": "error",
                "noUnusedTemplateLiteral": "error",
                "useNumberNamespace": "error",
                "noInferrableTypes": "error",
                "noUselessElse": "error",
            },
        },
    },
}
