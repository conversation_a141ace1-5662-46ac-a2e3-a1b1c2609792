name-template: "1.0.2 🌈"
tag-template: 1.0.2
exclude-labels:
    - "skip-changelog"

categories:
    - title: "🚀 Features"
      labels:
          - feature
          - enhancement
    - title: "🐛 Bug Fixes"
      labels:
          - fix
          - bugfix
          - bug
    - title: "🧰 Maintenance"
      label: chore
    - title: ":package: Dependencies"
      labels:
          - dependencies
          - build
          - dependabot
          - DEPENDABOT
version-resolver:
    major:
        labels:
            - major
    minor:
        labels:
            - minor
    patch:
        labels:
            - patch
    default: patch
autolabeler:
    - label: chore
      files:
          - "*.md"
      branch:
          - '/docs{0,1}\/.+/'
    - label: bug
      branch:
          - /fix\/.+/
      title:
          - /fix/i
    - label: enhancement
      branch:
          - /feature\/.+/
      body:
          - "/JIRA-[0-9]{1,4}/"
template: |
    ## Changes

    $CHANGES
