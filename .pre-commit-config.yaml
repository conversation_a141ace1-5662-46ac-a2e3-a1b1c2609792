fail_fast: false
default_language_version:
    python: python3
default_stages:
    - pre-commit
    - pre-push
minimum_pre_commit_version: 2.16.0
repos:
    - repo: https://github.com/biomejs/pre-commit
      rev: v2.2.3
      hooks:
          - id: biome-format
    - repo: https://github.com/astral-sh/ruff-pre-commit
      rev: v0.12.12
      hooks:
          - id: ruff-check
            args: [--fix, --exit-non-zero-on-fix, --unsafe-fixes]
          - id: ruff-format
    - repo: https://github.com/pre-commit/pre-commit-hooks
      rev: v6.0.0
      hooks:
          - id: detect-private-key
          - id: check-ast
          - id: end-of-file-fixer
          - id: mixed-line-ending
            args: [--fix=lf]
          - id: trailing-whitespace
          - id: check-case-conflict
          - id: check-added-large-files
          - id: check-toml
          - id: check-yaml
          - id: check-merge-conflict
          - id: no-commit-to-branch
            args: ["--branch=main"]
    - repo: https://github.com/pre-commit/mirrors-mypy
      rev: v1.17.1
      hooks:
          - id: mypy
            args: [--no-strict-optional, --ignore-missing-imports]
            additional_dependencies:
                ["types-setuptools", "types-requests", "types-attrs"]
    - repo: local
      hooks:
          - id: forbid-to-commit
            name: Don't commit rej files
            entry: |
                Cannot commit .rej files. These indicate merge conflicts that arise during automated template updates.
                Fix the merge conflicts manually and remove the .rej files.
            language: fail
            files: '.*\.rej$'
