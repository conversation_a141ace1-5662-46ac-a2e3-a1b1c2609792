# pertpy

Pertpy is a scverse ecosystem framework for analyzing large-scale single-cell perturbation experiments.
It provides tools for harmonizing perturbation datasets, automating metadata annotation, calculating perturbation distances, and efficiently analyzing how cells respond to various stimuli like genetic modifications, drug treatments, and environmental changes.

![overview](https://github.com/user-attachments/assets/d2e32d69-b767-4be3-a938-77a9dce45d3f)

```{eval-rst}
.. card:: Installation :octicon:`plug;1em;`
    :link: installation
    :link-type: doc

    New to *pertpy*? Check out the installation guide.
```

```{eval-rst}
.. card:: API reference :octicon:`book;1em;`
    :link: api
    :link-type: doc

    The API reference contains a detailed description of the pertpy API.
```

```{eval-rst}
.. card:: Tutorials :octicon:`play;1em;`
    :link: tutorials
    :link-type: doc

    The tutorials walk you through real-world applications of pertpy.
```

```{eval-rst}
.. card:: Discussion :octicon:`megaphone;1em;`
    :link: https://discourse.scverse.org/

    Need help? Reach out on our forum to get your questions answered!

```

```{eval-rst}
.. card:: GitHub :octicon:`mark-github;1em;`
    :link: https://github.com/scverse/pertpy

    Found a bug? Interested in improving pertpy? Checkout our GitHub for the latest developments.

```

```{toctree}
:caption: 'General'
:hidden: true
:maxdepth: 2

installation
api
contributing
changelog
references
```

```{toctree}
:caption: 'Gallery'
:hidden: true
:maxdepth: 3

tutorials
usecases
```

```{toctree}
:caption: 'About'
:hidden: true
:maxdepth: 2

about/background
about/cite
GitHub <https://github.com/scverse/pertpy>
Discourse <https://discourse.scverse.org/c/ecosystem/pertpy/46>
```

## Citation

```bibtex
@article {Heumos2024.08.04.606516,
    author = {Heumos, Lukas and Ji, Yuge and May, Lilly and Green, Tessa and Zhang, Xinyue and Wu, Xichen and Ostner, Johannes and Peidli, Stefan and Schumacher, Antonia and Hrovatin, Karin and Müller, Michaela and Chong, Faye and Sturm, Gregor and Tejada, Alejandro and Dann, Emma and Dong, Mingze and Bahrami, Mojtaba and Gold, Ilan and Rybakov, Sergei and Namsaraeva, Altana and Moinfar, Amir and Zheng, Zihe and Roellin, Eljas and Mekki, Isra and Sander, Chris and Lotfollahi, Mohammad and Schiller, Herbert B. and Theis, Fabian J.},
    title = {Pertpy: an end-to-end framework for perturbation analysis},
    elocation-id = {2024.08.04.606516},
    year = {2024},
    doi = {10.1101/2024.08.04.606516},
    publisher = {Cold Spring Harbor Laboratory},
    URL = {https://www.biorxiv.org/content/early/2024/08/07/2024.08.04.606516},
    eprint = {https://www.biorxiv.org/content/early/2024/08/07/2024.08.04.606516.full.pdf},
    journal = {bioRxiv}
}
```

## NumFOCUS

[//]: # "numfocus-fiscal-sponsor-attribution"

pertpy is part of the scverse® project ([website](https://scverse.org), [governance](https://scverse.org/about/roles)) and is fiscally sponsored by [NumFOCUS](https://numfocus.org/).
If you like scverse® and want to support our mission, please consider making a tax-deductible [donation](https://numfocus.org/donate-to-scverse) to help the project pay for developer time, professional services, travel, workshops, and a variety of other needs.

<div align="center">
<a href="https://numfocus.org/project/scverse">
  <img
    src="https://raw.githubusercontent.com/numfocus/templates/master/images/numfocus-logo.png"
    width="200"
  >
</a>
</div>
