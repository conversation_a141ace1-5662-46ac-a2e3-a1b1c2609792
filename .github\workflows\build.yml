name: Check Build

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    # to fail on error in multiline statements (-e), in pipes (-o pipefail), and on unset variables (-u).
    shell: bash -euo pipefail {0}

jobs:
  package:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          filter: blob:none
          fetch-depth: 0

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          cache-dependency-glob: pyproject.toml

      - name: Build package
        run: uv build

      - name: Check package
        run: uvx twine check --strict dist/*.whl
