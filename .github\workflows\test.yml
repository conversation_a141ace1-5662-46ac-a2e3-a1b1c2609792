name: Test

on:
    push:
        branches: [main]
    pull_request:
        branches: [main]

concurrency:
    group: ${{ github.workflow }}-${{ github.ref }}
    cancel-in-progress: true

jobs:
    test:
        runs-on: ${{ matrix.os }}
        defaults:
            run:
              # to fail on error in multiline statements (-e), in pipes (-o pipefail), and on unset variables (-u).
              shell: bash -euo pipefail {0}

        strategy:
            fail-fast: false
            matrix:
                include:
                    - os: ubuntu-latest
                      python: "3.13"
                    - os: ubuntu-latest
                      python: "3.13"
                      pip-flags: "--pre"

        env:
            OS: ${{ matrix.os }}
            PYTHON: ${{ matrix.python }}

        steps:
            - uses: actions/checkout@v4
              with:
                filter: blob:none
                fetch-depth: 0

            - name: Cache .pertpy_cache
              uses: actions/cache@v4
              with:
                path: cache
                key: ${{ runner.os }}-pertpy-cache-${{ hashFiles('pertpy/metadata/**') }}
                restore-keys: |
                  ${{ runner.os }}-pertpy-cache

            - name: Set up Python ${{ matrix.python }}
              uses: actions/setup-python@v5
              with:
                  python-version: ${{ matrix.python }}
            - name: Install R
              uses: r-lib/actions/setup-r@v2
              with:
                  r-version: "4.4.3"

            - name: Cache R packages
              id: r-cache
              uses: actions/cache@v3
              with:
                path: ${{ env.R_LIBS_USER }}
                key: ${{ runner.os }}-r-${{ hashFiles('**/pertpy/tools/_milo.py') }}
                restore-keys: ${{ runner.os }}-r-

            - name: Install R dependencies
              if: steps.r-cache.outputs.cache-hit != 'true'
              run: |
                mkdir -p ${{ env.R_LIBS_USER }}
                Rscript --vanilla -e "install.packages(c('BiocManager', 'statmod'), repos='https://cran.r-project.org'); BiocManager::install('edgeR', lib='${{ env.R_LIBS_USER }}')"

            - name: Install uv
              uses: astral-sh/setup-uv@v6
              with:
                enable-cache: true
                cache-dependency-glob: pyproject.toml
            - name: Install dependencies
              run: |
                  uv pip install --system rpy2
                  uv pip install --system ${{ matrix.pip-flags }} ".[dev,test,all]"

            - name: Test
              env:
                  MPLBACKEND: agg
                  PLATFORM: ${{ matrix.os }}
                  DISPLAY: :42
              run: |
                  pytest_args="-m pytest -v --color=yes"
                  coverage run $pytest_args

            - name: Show coverage report
              run: coverage report -m

            - name: Upload coverage
              uses: codecov/codecov-action@v4
              with:
                  token: ${{ secrets.CODECOV_TOKEN }}
                  fail_ci_if_error: true
                  verbose: true
