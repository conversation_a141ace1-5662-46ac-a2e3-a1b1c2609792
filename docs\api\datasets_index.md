```{eval-rst}
.. currentmodule:: pertpy
```

# Datasets

pertpy provides access to several curated single-cell datasets spanning several types of perturbations.
Many of the datasets originate from [scperturb](http://projects.sanderlab.org/scperturb/) {cite}`Peidli2024`.

```{eval-rst}
.. autosummary::
    :toctree: data

    data.adamson_2016_pilot
    data.adamson_2016_upr_epistasis
    data.adamson_2016_upr_perturb_seq
    data.aissa_2021
    data.bhattacherjee
    data.bur<PERSON>ynski_crohn
    data.chang_2021
    data.combosciplex
    data.cinemaot_example
    data.datlinger_2017
    data.datlinger_2021
    data.dialogue_example
    data.distance_example
    data.dixit_2016
    data.dixit_2016_raw
    data.dong_2023
    data.frangieh_2021
    data.frangieh_2021_protein
    data.frangieh_2021_raw
    data.frangieh_2021_rna
    data.gasperini_2019_atscale
    data.gasperini_2019_highmoi
    data.gasperini_2019_lowmoi
    data.gehring_2019
    data.haber_2017_regions
    data.hagai_2018
    data.kang_2018
    data.mcfarland_2020
    data.norman_2019
    data.norman_2019_raw
    data.papalexi_2021
    data.replogle_2022_k562_essential
    data.replogle_2022_k562_gwps
    data.replogle_2022_rpe1
    data.sc_sim_augur
    data.schiebinger_2019_16day
    data.schiebinger_2019_18day
    data.schraivogel_2020_tap_screen_chr8
    data.schraivogel_2020_tap_screen_chr11
    data.sciplex_gxe1
    data.sciplex3_raw
    data.shifrut_2018
    data.smillie_2019
    data.srivatsan_2020_sciplex2
    data.srivatsan_2020_sciplex3
    data.srivatsan_2020_sciplex4
    data.stephenson_2021_subsampled
    data.tasccoda_example
    data.tian_2019_day7neuron
    data.tian_2019_ipsc
    data.tian_2021_crispra
    data.tian_2021_crispri
    data.weinreb_2020
    data.xie_2017
    data.zhao_2021
    data.zhang_2021
```
