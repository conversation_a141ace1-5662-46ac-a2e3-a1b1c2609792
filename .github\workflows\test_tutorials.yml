name: Test tutorials

on:
    push:
        branches: [main]
    pull_request:
        branches: [main]

concurrency:
    group: ${{ github.workflow }}-${{ github.ref }}
    cancel-in-progress: true

jobs:
    test:
        runs-on: ubuntu-latest
        defaults:
            run:
              # to fail on error in multiline statements (-e), in pipes (-o pipefail), and on unset variables (-u).
              shell: bash -euo pipefail {0}

        env:
            NOTEBOOK_PATH: docs/tutorials/notebooks

        strategy:
            fail-fast: false
            matrix:
                notebook: [
                        "augur.ipynb",
                        "cinemaot.ipynb",
                        "differential_gene_expression.ipynb",
                        "distances.ipynb",
                        "distance_tests.ipynb",
                        "enrichment.ipynb",
                        "guide_rna_assignment.ipynb",
                        "milo.ipynb",
                        "mixscape.ipynb",
                        "sccoda.ipynb",
                        # "perturbation_space.ipynb",  seems to run OOM as of Jax implementation
                        # "dialogue.ipynb", takes too long
                        # "tasccoda.ipynb", a pain to get running because of the required QT dependency. The QT action leads to a dead kernel
                        # also no use cases yet
                    ]

        steps:
            - uses: actions/checkout@v4
              with:
                filter: blob:none
                fetch-depth: 0
                submodules: "true"

            - name: Cache .pertpy_cache
              uses: actions/cache@v4
              with:
                path: cache
                key: ${{ runner.os }}-pertpy-cache-${{ hashFiles('pertpy/metadata/**') }}
                restore-keys: |
                  ${{ runner.os }}-pertpy-cache

            - name: Set up Python
              uses: actions/setup-python@v5
              with:
                  python-version: "3.13"
            - name: Install R
              uses: r-lib/actions/setup-r@v2
              with:
                  r-version: "4.4.3"

            - name: Cache R packages
              id: r-cache
              uses: actions/cache@v3
              with:
                path: ${{ env.R_LIBS_USER }}
                key: ${{ runner.os }}-r-${{ hashFiles('**/pertpy/tools/_milo.py') }}
                restore-keys: ${{ runner.os }}-r-

            - name: Install R dependencies
              if: steps.r-cache.outputs.cache-hit != 'true'
              run: |
                mkdir -p ${{ env.R_LIBS_USER }}
                Rscript --vanilla -e "install.packages(c('BiocManager', 'statmod'), repos='https://cran.r-project.org'); BiocManager::install('edgeR', lib='${{ env.R_LIBS_USER }}')"

            - name: Install uv
              uses: astral-sh/setup-uv@v6
              with:
                enable-cache: true
                cache-dependency-glob: pyproject.toml
            - name: Install dependencies
              run: |
                  uv pip install --system rpy2 decoupler muon
                  uv pip install --system ${{ matrix.pip-flags }} ".[dev,test,all]"
                  uv pip install --system nbconvert ipykernel

            - name: Run ${{ matrix.notebook }} Notebook
              run: jupyter nbconvert --to notebook --execute ${{ env.NOTEBOOK_PATH }}/${{ matrix.notebook }}
