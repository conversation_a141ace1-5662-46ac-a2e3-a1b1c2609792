[build-system]
build-backend = "hatchling.build"
requires = ["hatchling"]

[project]
name = "pertpy"
version = "1.0.2"
description = "Perturbation Analysis in the scverse ecosystem."
readme = "README.md"
requires-python = ">=3.11,<3.14"
license = {file = "LICENSE"}
authors = [
    {name = "<PERSON><PERSON> He<PERSON>"},
    {name = "<PERSON>"},
    {name = "<PERSON>"},
    {name = "<PERSON>"},
    {name = "<PERSON>"},
    {name = "<PERSON>"},
    {name = "<PERSON><PERSON>"},
    {name = "<PERSON><PERSON><PERSON>"},
    {name = "<PERSON><PERSON>"},
    {name = "<PERSON>"},
    {name = "<PERSON><PERSON>"},
]
maintainers = [
    {name = "<PERSON><PERSON> Heum<PERSON>", email = "<EMAIL>"},
]
urls.Documentation = "https://pertpy.readthedo<PERSON>.io"
urls.Source = "https://github.com/scverse/pertpy"
urls.Home-page = "https://github.com/scverse/pertpy"

classifiers = [
    "License :: OSI Approved :: Apache Software License",
    "Development Status :: 5 - Production/Stable",
    "Environment :: Console",
    "Framework :: Jupyter",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "Natural Language :: English",
    "Operating System :: MacOS :: MacOS X",
    "Operating System :: POSIX :: Linux",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Scientific/Engineering :: Bio-Informatics",
    "Topic :: Scientific/Engineering :: Visualization",
]

dependencies = [
    "requests",
    "rich",
    "scanpy",
    "mudata",
    "scikit-misc",
    "adjusttext",
    "ott-jax",
    "numpyro",
    "flax",
    "sparsecca",
    "openpyxl",
    "pubchempy",
    "pyarrow",
    "blitzgsea",
    "scikit-learn>=1.4",
    "fast-array-utils[accel,sparse]",
    "lamin_utils",
    "arviz",
]

[project.optional-dependencies]
tcoda = [
    "toytree>=3.0",
    "ete4",
    "pyqt6"
]
de = [
    "formulaic-contrasts>=0.2.0",
    "formulaic",
    "pydeseq2>=v0.5.0",
]
scgen = ["scvi-tools"]
all = [
    "pertpy[tcoda,de,scgen]"
]
dev = [
    "pre-commit",
]
doc = [
    "docutils>=0.8",
    "sphinx>=8.1",
    "scanpydoc",
    "sphinx-book-theme",
    "myst-nb",
    "sphinxcontrib-bibtex>=1.0.0",
    "sphinx-issues",
    "sphinx-gallery",
    "sphinx-autodoc-typehints",
    "sphinx-copybutton",
    "sphinx-remove-toctrees",
    "sphinx-design",
    "sphinx-last-updated-by-git",
    "sphinx-automodapi",
    "sphinxext-opengraph",
    "pygments",
    "nbsphinx",
    "nbsphinx-link",
    "ipykernel",
    "ipython",
]
test = [
    "pytest",
    "coverage",
    "leidenalg"
]

[tool.hatch.version]
source = "vcs"

[tool.hatch.metadata]
allow-direct-references = true

[tool.coverage.run]
source_pkgs = ["pertpy"]
omit = [
    "**/test_*.py",
]

[tool.pytest.ini_options]
testpaths = "tests"
xfail_strict = true
addopts = [
    "--import-mode=importlib",  # allow using test files with same name
]
markers = [
    "conda: marks a subset of tests to be ran on the Bioconda CI.",
    "extra: marks tests that require extra dependencies.",
]
minversion = 6.0
norecursedirs = [ '.*', 'build', 'dist', '*.egg', 'data', '__pycache__']
filterwarnings = [
    "ignore::Warning:statsmodels.*",
    "ignore:Jupyter is migrating its paths to use standard platformdirs:DeprecationWarning",
    "ignore:lbfgs failed to converge",
    "ignore:Mean of empty slice:RuntimeWarning",
    "ignore:invalid value encountered in divide",
    "ignore:Importing read_csv from `anndata` is deprecated:FutureWarning",
    "ignore:Importing read_loom from `anndata` is deprecated:FutureWarning",
    "ignore:Importing read_text from `anndata` is deprecated:FutureWarning",
    "ignore:Importing CSCDataset from `anndata.experimental` is deprecated:FutureWarning",
    "ignore:Importing CSRDataset from `anndata.experimental` is deprecated:FutureWarning",
    "ignore:Importing read_elem from `anndata.experimental` is deprecated:FutureWarning",
    "ignore:ast.NameConstant is deprecated and will be removed in Python 3.14; use ast.Constant instead:DeprecationWarning",
    "ignore:'cgi' is deprecated and slated for removal in Python 3.13:DeprecationWarning",
    "ignore:In the future, the default backend for leiden will be igraph instead of leidenalg:FutureWarning",
    "ignore:Transforming to str index:anndata.ImplicitModificationWarning",
    "ignore:Failed to correctly find n_neighbors for some samples:UserWarning"
]

[tool.hatch.envs.default]
installer = "uv"
features = [ "dev" ]

[tool.hatch.envs.docs]
features = [ "doc" ]
scripts.build = "sphinx-build -M html docs docs/_build -W {args}"
scripts.open = "python -m webbrowser -t docs/_build/html/index.html"
scripts.clean = "git clean -fdX -- {args:docs}"

[tool.hatch.envs.hatch-test]
features = [ "test", "tcoda" ]

[tool.ruff]
src = ["src"]
line-length = 120

[tool.ruff.format]
docstring-code-format = true

[tool.ruff.lint]
select = [
    "F",      # Errors detected by Pyflakes
    "E",      # Error detected by Pycodestyle
    "W",      # Warning detected by Pycodestyle
    "I",      # isort
    "D",      # pydocstyle
    "B",      # flake8-bugbear
    "TID",    # flake8-tidy-imports
    "C4",     # flake8-comprehensions
    "BLE",    # flake8-blind-except
    "UP",     # pyupgrade
    "RUF100", # Report unused noqa directives
    "TCH",    # Typing imports
    "NPY",    # Numpy specific rules
    "PTH",    # Use pathlib
    "PERF",   # Performance
    "PIE",    # Syntax simplification
    "SIM",    # Code simplifications
    "TID",    # banned imports
    "TC",     # type checking sections
    "PL"      # pylint
]
ignore = [
    # line too long -> we accept long comment lines; black gets rid of long code lines
    "E501",
    # Do not assign a lambda expression, use a def -> lambda expression assignments are convenient
    "E731",
    # allow I, O, l as variable names -> I is the identity matrix
    "E741",
    # Missing docstring in public package
    "D104",
    # Missing docstring in public module
    "D100",
    # Missing docstring in __init__
    "D107",
    # Errors from function calls in argument defaults. These are fine when the result is immutable.
    "B008",
    # __magic__ methods are are often self-explanatory, allow missing docstrings
    "D105",
    # first line should end with a period [Bug: doesn't work with single-line docstrings]
    "D400",
    # First line should be in imperative mood; try rephrasing
    "D401",
    ## Disable one in each pair of mutually incompatible rules
    # We don’t want a blank line before a class docstring
    "D203",
    # We want docstrings to start immediately after the opening triple quote
    "D213",
    # Imports unused
    "F401",
    # camcelcase imported as lowercase
    "N813",
    # module import not at top level of file
    "E402",
    # Too many arguments in function definition
    "PLR0913",
    # Too many branches
    "PLR0912",
    # magic value used in comparison
    "PLR2004",
    # Too many statements
    "PLR0915",
    # Extracting value from dictionary without calling `.items()` - clashes with sim
    "PLC0206",
    # import should be at top of file
    "PLC0415"
]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.per-file-ignores]
"docs/*" = ["I"]
"tests/*" = ["D"]
"*/__init__.py" = ["F401"]

[tool.mypy]
strict = false
pretty = true
show_column_numbers = true
show_error_codes = true
show_error_context = true
ignore_missing_imports = true
no_strict_optional = true

[tool.cruft]
skip = [
    "tests",
    "src/**/__init__.py",
    "src/**/basic.py",
    "docs/api.md",
    "docs/changelog.md",
    "docs/references.bib",
    "docs/references.md",
    "docs/notebooks/example.ipynb"
]
