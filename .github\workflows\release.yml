name: Release

on:
  release:
    types: [published]

defaults:
  run:
    shell: bash -euo pipefail {0}

jobs:
  release:
    name: Upload release to PyPI
    runs-on: ubuntu-latest
    environment:
      name: pypi
      url: https://pypi.org/p/pertpy
    permissions:
      id-token: write
    steps:
      - uses: actions/checkout@v4
        with:
          filter: blob:none
          fetch-depth: 0

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          cache-dependency-glob: pyproject.toml

      - name: Build package
        run: uv build
      - name: Publish package distributions to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
