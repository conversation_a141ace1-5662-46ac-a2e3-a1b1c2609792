name: Release Drafter
on:
    push:
        branches:
            - main
    pull_request:
        branches:
            - main
        types:
            - opened
            - reopened
            - synchronize
jobs:
    update_release_draft:
        runs-on: ubuntu-latest
        steps:
            - uses: release-drafter/release-drafter@v5
              env:
                  GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
